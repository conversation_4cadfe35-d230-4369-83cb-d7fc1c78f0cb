import { headers } from "next/headers";
import Link from "next/link";
import { PageLayout } from "@/components/core";
import { ProfessionalProjectList, ProjectCard } from "@/components/features";
import { getQueryClient, trpc } from "@/components/integrations/trpc";
import { buttonVariants } from "@/components/ui";
import { auth } from "@/lib/auth";

export default async function JobsPage() {
  const session = await auth.api.getSession({ headers: await headers() });
  const isProfessional = session?.user?.role === "contractor";

  const queryClient = getQueryClient();
  const jobs = isProfessional
    ? null
    : await queryClient.fetchQuery(trpc.projects.listForUser.queryOptions());

  const actions = !isProfessional ? (
    <Link
      href="/projects/new"
      className={buttonVariants({ variant: "default" })}
    >
      New Project
    </Link>
  ) : null;

  return (
    <PageLayout title="Projects" actions={actions}>
      {isProfessional ? (
        <ProfessionalProjectList />
      ) : (
        <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 sm:gap-6 lg:grid-cols-3 xl:grid-cols-4">
          {jobs?.map((job) => <ProjectCard key={job.id} project={job} />)}
        </div>
      )}
    </PageLayout>
  );
}

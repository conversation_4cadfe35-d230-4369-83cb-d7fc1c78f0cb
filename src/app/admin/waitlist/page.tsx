"use client";

import { SearchIcon } from "lucide-react";
import Link from "next/link";
import { useSearchParams } from "next/navigation";
import { WaitlistStats, WaitlistTable } from "@/components/features/waitlist";
import { Badge } from "@/components/ui/badge";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Skeleton } from "@/components/ui/skeleton";

export default function WaitlistPage() {
  const searchParams = useSearchParams();

  const statusParam = searchParams.get("status");
  const status =
    statusParam === "PENDING" ||
    statusParam === "APPROVED" ||
    statusParam === "REJECTED" ||
    statusParam === "INVITED"
      ? (statusParam as "PENDING" | "APPROVED" | "REJECTED" | "INVITED")
      : null;

  const typeParam = searchParams.get("type");
  const accountType =
    typeParam === "homeowner" || typeParam === "contractor"
      ? (typeParam as "homeowner" | "contractor")
      : null;

  const handleSearch = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    const formData = new FormData(e.currentTarget);
    const searchQuery = formData.get("search") as string;

    if (searchQuery) {
      // In a real implementation, you would add search functionality
      // For now, we'll just log it
      console.log("Search query:", searchQuery);
    }
  };

  return (
    <div className="flex-1 space-y-6 p-8 pt-6">
      {/* Header Section */}
      <div className="flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
        <div className="space-y-2">
          <h1 className="font-bold text-3xl tracking-tight">
            Waitlist Management
          </h1>
          <p className="text-muted-foreground">
            Review and manage waitlist applications for the platform
          </p>
        </div>
      </div>

      {/* Waitlist Statistics */}
      <WaitlistStats />

      {/* Filters and Search */}
      <Card>
        <CardHeader>
          <CardTitle>Waitlist Entries</CardTitle>
          <CardDescription>
            Search and filter waitlist entries by status and account type
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col gap-4 md:flex-row md:items-center">
            <div className="relative flex-1">
              <form onSubmit={handleSearch}>
                <SearchIcon className="-translate-y-1/2 absolute top-1/2 left-3 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Search by name or email..."
                  className="pl-10"
                  name="search"
                />
              </form>
            </div>
            <div className="flex flex-wrap gap-2">
              <Badge variant={!status ? "default" : "secondary"}>
                <Link href="/admin/waitlist">All</Link>
              </Badge>
              <Badge variant={status === "PENDING" ? "default" : "secondary"}>
                <Link href="/admin/waitlist?status=PENDING">Pending</Link>
              </Badge>
              <Badge variant={status === "APPROVED" ? "default" : "secondary"}>
                <Link href="/admin/waitlist?status=APPROVED">Approved</Link>
              </Badge>
              <Badge variant={status === "INVITED" ? "default" : "secondary"}>
                <Link href="/admin/waitlist?status=INVITED">Invited</Link>
              </Badge>
              <Badge variant={status === "REJECTED" ? "default" : "secondary"}>
                <Link href="/admin/waitlist?status=REJECTED">Rejected</Link>
              </Badge>
            </div>
          </div>

          <div className="mt-4 flex flex-wrap gap-2">
            <Badge variant={!accountType ? "default" : "secondary"}>
              <Link
                href={
                  status
                    ? `/admin/waitlist?status=${status}`
                    : "/admin/waitlist"
                }
              >
                All Types
              </Link>
            </Badge>
            <Badge
              variant={accountType === "homeowner" ? "default" : "secondary"}
            >
              <Link
                href={
                  status
                    ? `/admin/waitlist?status=${status}&type=homeowner`
                    : "/admin/waitlist?type=homeowner"
                }
              >
                Homeowners
              </Link>
            </Badge>
            <Badge
              variant={accountType === "contractor" ? "default" : "secondary"}
            >
              <Link
                href={
                  status
                    ? `/admin/waitlist?status=${status}&type=contractor`
                    : "/admin/waitlist?type=contractor"
                }
              >
                Contractors
              </Link>
            </Badge>
          </div>
        </CardContent>
      </Card>

      {/* Waitlist Table */}
      <WaitlistTable
        initialStatus={status || undefined}
        initialAccountType={accountType || undefined}
      />
    </div>
  );
}

function WaitlistStatsSkeleton() {
  return (
    <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
      {Array(4)
        .fill(0)
        .map((_, i) => (
          // biome-ignore lint/suspicious/noArrayIndexKey: Static skeleton items
          <Card key={i}>
            <CardContent className="flex items-center p-6">
              <Skeleton className="h-8 w-8 rounded" />
              <div className="ml-4 space-y-2">
                <Skeleton className="h-4 w-20" />
                <Skeleton className="h-6 w-12" />
              </div>
            </CardContent>
          </Card>
        ))}
    </div>
  );
}

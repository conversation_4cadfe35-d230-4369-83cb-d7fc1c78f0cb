import type { Metadata } from "next";
import { env } from "@/env";
import { PostHogDebug } from "@/components/debug/posthog-debug";
import { WaitlistTest } from "@/components/debug/waitlist-test";

export const metadata: Metadata = {
  metadataBase: new URL(env.APP_URL),
  title: "TradeCrews | Connecting Homeowners with Trusted Trade Professionals",
  description:
    "Find reliable contractors for your home projects or grow your trade business by connecting with homeowners looking for your expertise.",
  keywords:
    "trade professionals, contractors, home improvement, construction, plumbing, electrical, renovation",
  openGraph: {
    title:
      "TradeCrews | The Platform for Home Projects and Trade Professionals",
    description:
      "Connect with trusted contractors or find new clients for your trade business. TradeCrews makes home projects simple and reliable.",
    images: ["/images/tradecrews-og.png"],
    type: "website",
  },
};

export default function MarketingLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <>
      {children}
      <WaitlistTest />
      <PostHogDebug />
    </>
  );
}

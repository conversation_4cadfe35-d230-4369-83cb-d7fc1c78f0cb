"use client";

/**
 * Cookie utilities for client-side cookie management
 * Provides type-safe cookie operations with proper error handling
 */

export type CookieOptions = {
  expires?: Date | number; // Date object or days from now
  maxAge?: number; // seconds
  path?: string;
  domain?: string;
  secure?: boolean;
  sameSite?: "strict" | "lax" | "none";
  httpOnly?: boolean; // Note: httpOnly cookies can't be accessed via JavaScript
};

/**
 * Get a cookie value by name
 * @param name Cookie name
 * @returns Cookie value or null if not found
 */
export function getCookie(name: string): string | null {
  if (typeof document === "undefined") {
    return null;
  }

  try {
    const value = `; ${document.cookie}`;
    const parts = value.split(`; ${name}=`);
    
    if (parts.length === 2) {
      const cookieValue = parts.pop()?.split(';').shift();
      return cookieValue ? decodeURIComponent(cookieValue) : null;
    }
    
    return null;
  } catch (error) {
    console.warn(`Error reading cookie "${name}":`, error);
    return null;
  }
}

/**
 * Set a cookie with options
 * @param name <PERSON>ie name
 * @param value Cookie value
 * @param options Cookie options
 */
export function setCookie(
  name: string,
  value: string,
  options: CookieOptions = {}
): void {
  if (typeof document === "undefined") {
    return;
  }

  try {
    let cookieString = `${name}=${encodeURIComponent(value)}`;

    // Handle expires option
    if (options.expires) {
      let expiresDate: Date;
      
      if (typeof options.expires === "number") {
        // If number, treat as days from now
        expiresDate = new Date();
        expiresDate.setTime(expiresDate.getTime() + options.expires * 24 * 60 * 60 * 1000);
      } else {
        expiresDate = options.expires;
      }
      
      cookieString += `; expires=${expiresDate.toUTCString()}`;
    }

    // Handle maxAge option (takes precedence over expires)
    if (options.maxAge !== undefined) {
      cookieString += `; max-age=${options.maxAge}`;
    }

    // Handle path option
    if (options.path) {
      cookieString += `; path=${options.path}`;
    } else {
      cookieString += "; path=/"; // Default to root path
    }

    // Handle domain option
    if (options.domain) {
      cookieString += `; domain=${options.domain}`;
    }

    // Handle secure option
    if (options.secure) {
      cookieString += "; secure";
    }

    // Handle sameSite option
    if (options.sameSite) {
      cookieString += `; samesite=${options.sameSite}`;
    }

    // Note: httpOnly can't be set via JavaScript, it's server-side only
    if (options.httpOnly) {
      console.warn("httpOnly cookies cannot be set via JavaScript");
    }

    document.cookie = cookieString;
  } catch (error) {
    console.warn(`Error setting cookie "${name}":`, error);
  }
}

/**
 * Delete a cookie by name
 * @param name Cookie name
 * @param options Cookie options (path and domain should match the original cookie)
 */
export function deleteCookie(
  name: string,
  options: Pick<CookieOptions, "path" | "domain"> = {}
): void {
  setCookie(name, "", {
    ...options,
    expires: new Date(0), // Set to past date to delete
  });
}

/**
 * Check if a cookie exists
 * @param name Cookie name
 * @returns True if cookie exists
 */
export function hasCookie(name: string): boolean {
  return getCookie(name) !== null;
}

/**
 * Get all cookies as an object
 * @returns Object with cookie names as keys and values as values
 */
export function getAllCookies(): Record<string, string> {
  if (typeof document === "undefined") {
    return {};
  }

  try {
    const cookies: Record<string, string> = {};
    
    document.cookie.split(';').forEach(cookie => {
      const [name, ...rest] = cookie.trim().split('=');
      if (name && rest.length > 0) {
        const value = rest.join('='); // Handle values that contain '='
        cookies[name] = decodeURIComponent(value);
      }
    });
    
    return cookies;
  } catch (error) {
    console.warn("Error reading all cookies:", error);
    return {};
  }
}

/**
 * Generate a UUID v4 for anonymous user identification
 * @returns UUID v4 string
 */
export function generateAnonymousId(): string {
  // Use crypto.randomUUID if available (modern browsers)
  if (typeof crypto !== "undefined" && crypto.randomUUID) {
    return crypto.randomUUID();
  }

  // Fallback UUID v4 generation
  return "xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g, function (c) {
    const r = (Math.random() * 16) | 0;
    const v = c === "x" ? r : (r & 0x3) | 0x8;
    return v.toString(16);
  });
}

/**
 * PostHog-specific cookie utilities
 */
export const posthogCookieUtils = {
  /**
   * Get the PostHog cookie name for a given project API key
   * @param projectApiKey PostHog project API key
   * @returns PostHog cookie name
   */
  getCookieName(projectApiKey: string): string {
    return `ph_${projectApiKey}_posthog`;
  },

  /**
   * Get the PostHog distinct_id from the PostHog cookie
   * @param projectApiKey PostHog project API key
   * @returns Distinct ID or null if not found
   */
  getDistinctId(projectApiKey: string): string | null {
    const cookieName = this.getCookieName(projectApiKey);
    const cookieValue = getCookie(cookieName);
    
    if (!cookieValue) {
      return null;
    }

    try {
      const postHogData = JSON.parse(cookieValue);
      return postHogData.distinct_id || null;
    } catch (error) {
      console.warn("Error parsing PostHog cookie:", error);
      return null;
    }
  },

  /**
   * Get or create an anonymous ID for PostHog tracking
   * This creates a persistent anonymous ID that survives page reloads
   * @param projectApiKey PostHog project API key
   * @returns Anonymous distinct ID
   */
  getOrCreateAnonymousId(projectApiKey: string): string {
    // First try to get from PostHog cookie
    const existingId = this.getDistinctId(projectApiKey);
    if (existingId) {
      return existingId;
    }

    // Try to get from our custom anonymous ID cookie
    const anonymousCookieName = `tc_anonymous_id`;
    let anonymousId = getCookie(anonymousCookieName);
    
    if (!anonymousId) {
      // Generate new anonymous ID
      anonymousId = generateAnonymousId();
      
      // Store in cookie for 1 year
      setCookie(anonymousCookieName, anonymousId, {
        expires: 365, // 1 year
        sameSite: "lax",
        secure: process.env.NODE_ENV === "production",
      });
    }

    return anonymousId;
  },

  /**
   * Clear the anonymous ID cookie (useful for testing or privacy compliance)
   */
  clearAnonymousId(): void {
    deleteCookie("tc_anonymous_id");
  },
};

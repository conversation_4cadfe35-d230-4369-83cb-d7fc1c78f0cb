import { cookies, headers } from "next/headers";
import { env } from "@/env";
import { getPostHogServer } from "./posthog-server";

/**
 * Server-side utilities for PostHog anonymous user tracking
 * Handles anonymous user identification and tracking on the server
 */

/**
 * Extract PostHog distinct_id from cookies on the server
 * @param cookieStore Next.js cookies() instance
 * @returns distinct_id or null if not found
 */
export function getPostHogDistinctIdFromCookies(cookieStore: ReturnType<typeof cookies>): string | null {
  try {
    const postHogCookieName = `ph_${env.NEXT_PUBLIC_POSTHOG_KEY}_posthog`;
    const postHogCookie = cookieStore.get(postHogCookieName);
    
    if (!postHogCookie?.value) {
      return null;
    }

    const postHogData = JSON.parse(decodeURIComponent(postHogCookie.value));
    return postHogData.distinct_id || null;
  } catch (error) {
    console.warn("Error parsing PostHog cookie on server:", error);
    return null;
  }
}

/**
 * Get or create anonymous ID from cookies on the server
 * @param cookieStore Next.js cookies() instance
 * @returns anonymous distinct_id
 */
export function getOrCreateAnonymousIdServer(cookieStore: ReturnType<typeof cookies>): string {
  // First try to get from PostHog cookie
  const existingId = getPostHogDistinctIdFromCookies(cookieStore);
  if (existingId) {
    return existingId;
  }

  // Try to get from our custom anonymous ID cookie
  const anonymousCookie = cookieStore.get("tc_anonymous_id");
  if (anonymousCookie?.value) {
    return anonymousCookie.value;
  }

  // Generate new anonymous ID
  const anonymousId = crypto.randomUUID();
  
  // Store in cookie for 1 year
  cookieStore.set("tc_anonymous_id", anonymousId, {
    maxAge: 365 * 24 * 60 * 60, // 1 year in seconds
    sameSite: "lax",
    secure: process.env.NODE_ENV === "production",
    path: "/",
  });

  return anonymousId;
}

/**
 * Capture an event for anonymous users on the server
 * @param eventName Event name
 * @param properties Event properties
 * @param distinctId Optional distinct ID (will be auto-detected if not provided)
 */
export async function captureAnonymousEventServer(
  eventName: string,
  properties: Record<string, any> = {},
  distinctId?: string
): Promise<void> {
  try {
    const posthog = getPostHogServer();
    const cookieStore = await cookies();
    
    const finalDistinctId = distinctId || getOrCreateAnonymousIdServer(cookieStore);
    
    const eventProperties = {
      ...properties,
      $is_anonymous: true,
      $anonymous_id: finalDistinctId,
      $user_type: "anonymous",
    };

    posthog.capture({
      distinctId: finalDistinctId,
      event: eventName,
      properties: eventProperties,
    });

    await posthog.flush();
  } catch (error) {
    console.error("Error capturing anonymous event on server:", error);
  }
}

/**
 * Identify an anonymous user on the server
 * @param properties User properties
 * @param distinctId Optional distinct ID (will be auto-detected if not provided)
 */
export async function identifyAnonymousUserServer(
  properties: Record<string, any> = {},
  distinctId?: string
): Promise<void> {
  try {
    const posthog = getPostHogServer();
    const cookieStore = await cookies();
    
    const finalDistinctId = distinctId || getOrCreateAnonymousIdServer(cookieStore);
    
    const identifyProperties = {
      ...properties,
      $is_anonymous: true,
      $anonymous_id: finalDistinctId,
      $user_type: "anonymous",
    };

    posthog.identify({
      distinctId: finalDistinctId,
      properties: identifyProperties,
    });

    await posthog.flush();
  } catch (error) {
    console.error("Error identifying anonymous user on server:", error);
  }
}

/**
 * Track waitlist signup on the server
 * @param email User email
 * @param additionalProperties Additional properties
 * @param distinctId Optional distinct ID (will be auto-detected if not provided)
 */
export async function trackWaitlistSignupServer(
  email: string,
  additionalProperties: Record<string, any> = {},
  distinctId?: string
): Promise<void> {
  try {
    const posthog = getPostHogServer();
    const cookieStore = await cookies();
    const headersList = await headers();
    
    const finalDistinctId = distinctId || getOrCreateAnonymousIdServer(cookieStore);
    
    // Get additional context from headers
    const userAgent = headersList.get("user-agent") || undefined;
    const referer = headersList.get("referer") || undefined;
    
    const properties = {
      email,
      $is_anonymous: true,
      $user_type: "waitlist",
      $anonymous_id: finalDistinctId,
      user_agent: userAgent,
      referrer: referer,
      ...additionalProperties,
    };

    // First identify the user
    posthog.identify({
      distinctId: finalDistinctId,
      properties,
    });

    // Then capture the signup event
    posthog.capture({
      distinctId: finalDistinctId,
      event: "waitlist_signup",
      properties,
    });

    await posthog.flush();
  } catch (error) {
    console.error("Error tracking waitlist signup on server:", error);
  }
}

/**
 * Get feature flag value for anonymous users on the server
 * @param flagKey Feature flag key
 * @param defaultValue Default value if flag is not found
 * @param distinctId Optional distinct ID (will be auto-detected if not provided)
 * @returns Feature flag value
 */
export async function getFeatureFlagServer(
  flagKey: string,
  defaultValue: any = false,
  distinctId?: string
): Promise<any> {
  try {
    const posthog = getPostHogServer();
    const cookieStore = await cookies();
    
    const finalDistinctId = distinctId || getOrCreateAnonymousIdServer(cookieStore);
    
    const flagValue = await posthog.getFeatureFlag(flagKey, finalDistinctId);
    return flagValue ?? defaultValue;
  } catch (error) {
    console.error("Error getting feature flag on server:", error);
    return defaultValue;
  }
}

/**
 * Check if feature flag is enabled for anonymous users on the server
 * @param flagKey Feature flag key
 * @param distinctId Optional distinct ID (will be auto-detected if not provided)
 * @returns True if flag is enabled
 */
export async function isFeatureEnabledServer(
  flagKey: string,
  distinctId?: string
): Promise<boolean> {
  const flagValue = await getFeatureFlagServer(flagKey, false, distinctId);
  return flagValue === true;
}

/**
 * Middleware helper to extract anonymous ID from request
 * @param request Next.js request object
 * @returns anonymous distinct_id or null
 */
export function getAnonymousIdFromRequest(request: Request): string | null {
  try {
    const cookieHeader = request.headers.get("cookie");
    if (!cookieHeader) return null;

    // Try PostHog cookie first
    const postHogCookieMatch = cookieHeader.match(
      new RegExp(`ph_${env.NEXT_PUBLIC_POSTHOG_KEY}_posthog=([^;]+)`)
    );
    
    if (postHogCookieMatch?.[1]) {
      const postHogData = JSON.parse(decodeURIComponent(postHogCookieMatch[1]));
      if (postHogData.distinct_id) {
        return postHogData.distinct_id;
      }
    }

    // Try anonymous ID cookie
    const anonymousIdMatch = cookieHeader.match(/tc_anonymous_id=([^;]+)/);
    if (anonymousIdMatch?.[1]) {
      return decodeURIComponent(anonymousIdMatch[1]);
    }

    return null;
  } catch (error) {
    console.warn("Error extracting anonymous ID from request:", error);
    return null;
  }
}

/**
 * Types for server-side anonymous tracking
 */
export type ServerAnonymousEventProperties = {
  $is_anonymous: boolean;
  $anonymous_id: string;
  $user_type: "anonymous" | "waitlist";
  user_agent?: string;
  referrer?: string;
  [key: string]: any;
};

export type ServerWaitlistSignupProperties = ServerAnonymousEventProperties & {
  email: string;
  signup_source?: string;
  feature_interest?: string[];
  company_size?: string;
  role?: string;
};

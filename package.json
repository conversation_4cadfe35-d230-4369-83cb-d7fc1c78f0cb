{"name": "tradecrews-next", "version": "0.1.0", "private": true, "type": "module", "scripts": {"build": "next build", "build:analyze": "ANALYZE=true next build", "build:performance": "bun build && bun performance:lighthouse", "check": "biome check .", "check:unsafe": "biome check --write --unsafe .", "check:write": "biome check --write .", "db:generate": "drizzle-kit generate", "db:push": "drizzle-kit push", "db:studio": "drizzle-kit studio", "dev": "next dev --turbo", "dev:performance": "NODE_ENV=development next dev --turbo", "preview": "next build && next start", "start": "next start", "typecheck": "tsc --noEmit", "commit": "cz", "email:dev": "bun email dev --port 3001 --dir src/components/emails", "performance:lighthouse": "lighthouse http://localhost:3000 --output=html --output-path=./performance-report.html --chrome-flags='--headless'", "performance:bundle-size": "bun build:analyze", "performance:test": "node scripts/performance-test.js", "performance:full": "bun build && bun performance:test"}, "dependencies": {"@agentic/ai-sdk": "^7.6.7", "@agentic/wikipedia": "^7.6.7", "@ai-sdk/openai": "^1.3.22", "@ai-sdk/react": "^1.2.12", "@fullcalendar/core": "^6.1.17", "@fullcalendar/daygrid": "^6.1.17", "@fullcalendar/interaction": "^6.1.17", "@fullcalendar/react": "^6.1.17", "@fullcalendar/timegrid": "^6.1.17", "@hookform/resolvers": "^5.1.1", "@neondatabase/serverless": "^1.0.1", "@posthog/ai": "^5.1.0", "@radix-ui/themes": "^3.2.1", "@react-email/components": "^0.0.36", "@react-email/render": "1.1.2", "@t3-oss/env-nextjs": "^0.12.0", "@tanstack/react-query": "^5.80.7", "@tanstack/react-table": "^8.21.3", "@trpc/client": "^11.4.2", "@trpc/server": "^11.4.2", "@trpc/tanstack-react-query": "^11.4.2", "@uppy/core": "^4.4.6", "@uppy/dashboard": "^4.3.4", "@uppy/drag-drop": "^4.1.3", "@uppy/file-input": "^4.1.3", "@uppy/form": "^4.1.1", "@uppy/image-editor": "^3.3.3", "@uppy/progress-bar": "^4.2.1", "@uppy/react": "^4.3.0", "@uppy/transloadit": "^4.2.2", "@uppy/tus": "^4.2.2", "@zxcvbn-ts/core": "^3.0.4", "@zxcvbn-ts/language-common": "^3.0.4", "@zxcvbn-ts/language-en": "^3.0.2", "@zxcvbn-ts/matcher-pwned": "^3.0.4", "ai": "^4.3.16", "better-auth": "^1.2.9", "class-variance-authority": "^0.7.1", "client-only": "^0.0.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "contentlayer2": "^0.5.8", "date-fns": "^4.1.0", "driver.js": "^1.3.6", "drizzle-orm": "^0.44.2", "input-otp": "^1.4.2", "leaflet": "^1.9.4", "lucide-react": "^0.487.0", "next": "15.3.4", "next-contentlayer2": "^0.5.8", "next-themes": "^0.4.6", "next-ts-api": "^1.1.3", "openai": "^5.5.1", "pg": "^8.16.1", "posthog-js": "^1.255.0", "posthog-node": "^4.18.0", "pusher": "^5.2.0", "pusher-js": "^8.4.0", "radix-ui": "latest", "react": "^19.1.0", "react-day-picker": "9.7.0", "react-dom": "^19.1.0", "react-error-boundary": "^6.0.0", "react-hook-form": "^7.58.1", "react-icons": "^5.5.0", "react-inlinesvg": "^4.2.0", "react-leaflet": "^5.0.0", "react-qr-code": "^2.0.16", "recharts": "^2.15.3", "resend": "^4.6.0", "server-only": "^0.0.1", "sonner": "^2.0.5", "states-us": "^1.1.1", "superjson": "^2.2.2", "tailwind-merge": "^3.3.1", "transloadit": "^3.0.2", "tw-animate-css": "^1.3.4", "validator": "^13.15.15", "vaul": "^1.1.2", "web-push": "^3.6.7", "ws": "^8.18.2", "zod": "3.25.61"}, "devDependencies": {"@biomejs/biome": "2.0.0-beta.6", "@next/bundle-analyzer": "^15.3.3", "@paralleldrive/cuid2": "^2.2.2", "@snaplet/copycat": "^6.0.0", "@tailwindcss/postcss": "^4.1.10", "@types/leaflet": "^1.9.18", "@types/next-pwa": "^5.6.9", "@types/node": "^22.15.32", "@types/pg": "^8.15.4", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@types/validator": "^13.15.2", "@types/web-push": "^3.6.4", "@types/ws": "^8.18.1", "commitizen": "^4.3.1", "cz-conventional-changelog": "3.3.0", "drizzle-kit": "^0.31.1", "postcss": "^8.5.6", "prettier": "^3.5.3", "react-email": "4.0.16", "tailwindcss": "^4.1.10", "tsx": "^4.20.3", "typescript": "^5.8.3"}, "ct3aMetadata": {"initVersion": "7.39.2"}, "packageManager": "pnpm@10.12.1", "config": {"commitizen": {"path": "cz-conventional-changelog"}}, "prisma": {"seed": "tsx prisma/seed.ts"}, "@snaplet/seed": {"config": "src/lib/seed/seed.config.ts"}}